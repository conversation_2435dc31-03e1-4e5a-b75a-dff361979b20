# 订单系统重构实施方案

## 1. 概述

本文档描述了将现有的特定订单类型（如弹性伸缩订单）重构为通用订单系统的实施方案。采用基础订单表+扩展表的设计模式，提高系统的可扩展性和代码复用性。

## 2. 数据模型设计

### 2.1 核心数据模型

#### 2.1.1 基础订单模型 (Order)

基础订单表存储所有类型订单共有的字段：

| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | bigint | 主键ID |
| order_number | varchar(50) | 唯一订单号 |
| name | varchar(255) | 订单名称 |
| description | text | 订单描述 |
| type | varchar(50) | 订单类型（枚举值） |
| status | varchar(50) | 订单状态（枚举值） |
| executor | varchar(100) | 执行人 |
| execution_time | datetime | 执行时间 |
| created_by | varchar(100) | 创建人 |
| completion_time | datetime | 完成时间 |
| failure_reason | text | 失败原因 |
| created_at | datetime | 创建时间 |
| updated_at | datetime | 更新时间 |
| deleted_at | datetime | 删除时间（软删除） |

#### 2.1.2 弹性伸缩订单详情模型 (ElasticScalingOrderDetail)

弹性伸缩订单特有字段存储在扩展表中：

| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | bigint | 主键ID |
| order_id | bigint | 关联订单ID（外键） |
| cluster_id | bigint | 关联集群ID |
| strategy_id | bigint | 关联策略ID（可为NULL） |
| action_type | varchar(50) | 订单操作类型（入池/退池） |
| device_count | int | 请求的设备数量 |
| maintenance_start_time | datetime | 维护开始时间 |
| maintenance_end_time | datetime | 维护结束时间 |
| strategy_triggered_value | varchar(255) | 策略触发时的具体指标值 |
| strategy_threshold_value | varchar(255) | 策略触发时的阈值设定 |
| created_at | datetime | 创建时间 |
| updated_at | datetime | 更新时间 |
| deleted_at | datetime | 删除时间（软删除） |

#### 2.1.3 订单设备关联模型 (OrderDevice)

订单与设备的多对多关联表：

| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | bigint | 主键ID |
| order_id | bigint | 订单ID |
| device_id | bigint | 设备ID |
| status | varchar(50) | 处理状态 |
| created_at | datetime | 创建时间 |
| updated_at | datetime | 更新时间 |
| deleted_at | datetime | 删除时间（软删除） |

### 2.2 枚举值定义

#### 2.2.1 订单类型 (OrderType)

```go
// OrderType 订单类型枚举
type OrderType string

const (
    OrderTypeElasticScaling OrderType = "elastic_scaling" // 弹性伸缩
    OrderTypeMaintenance    OrderType = "maintenance"     // 设备维护
    OrderTypeDeployment     OrderType = "deployment"      // 应用部署
    // 可扩展更多类型...
)
```

#### 2.2.2 订单状态 (OrderStatus)

```go
// OrderStatus 订单状态枚举
type OrderStatus string

const (
    OrderStatusPending     OrderStatus = "pending"     // 待处理
    OrderStatusProcessing  OrderStatus = "processing"  // 处理中
    OrderStatusCompleted   OrderStatus = "completed"   // 已完成
    OrderStatusFailed      OrderStatus = "failed"      // 失败
    OrderStatusCancelled   OrderStatus = "cancelled"   // 已取消
    OrderStatusIgnored     OrderStatus = "ignored"     // 已忽略
    // 可扩展更多状态...
)
```

## 3. 实施步骤

### 3.1 数据库迁移

1. 创建新的基础订单表 `orders`
2. 创建弹性伸缩订单详情表 `elastic_scaling_order_details`
3. 迁移现有 `elastic_scaling_order` 表数据到新表结构
4. 更新 `order_device` 表中的外键关联

### 3.2 代码重构

1. 创建新的订单模型 `Order` 和详情模型 `ElasticScalingOrderDetail`
2. 更新现有的服务层代码，适配新的数据模型
3. 更新 API 接口，支持通用订单操作
4. 更新前端代码，适配新的 API 接口

### 3.3 迁移脚本

创建数据迁移脚本，将现有订单数据迁移到新的表结构：

```sql
-- 1. 创建新表
CREATE TABLE IF NOT EXISTS orders (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    order_number VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    type VARCHAR(50) NOT NULL,
    status VARCHAR(50) NOT NULL,
    executor VARCHAR(100),
    execution_time DATETIME,
    created_by VARCHAR(100),
    completion_time DATETIME,
    failure_reason TEXT,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    deleted_at DATETIME
);

CREATE TABLE IF NOT EXISTS elastic_scaling_order_details (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    order_id BIGINT UNIQUE NOT NULL,
    cluster_id BIGINT NOT NULL,
    strategy_id BIGINT,
    action_type VARCHAR(50) NOT NULL,
    device_count INT NOT NULL,
    maintenance_start_time DATETIME,
    maintenance_end_time DATETIME,
    external_ticket_id VARCHAR(100),
    strategy_triggered_value VARCHAR(255),
    strategy_threshold_value VARCHAR(255),
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    deleted_at DATETIME,
    FOREIGN KEY (order_id) REFERENCES orders(id)
);

-- 2. 迁移数据
INSERT INTO orders (
    id, order_number, name, description, type, status, 
    executor, execution_time, created_by, completion_time, 
    failure_reason, created_at, updated_at, deleted_at
)
SELECT 
    id, order_number, name, description, 'elastic_scaling', status,
    executor, execution_time, created_by, completion_time,
    failure_reason, created_at, updated_at, deleted_at
FROM elastic_scaling_order;

INSERT INTO elastic_scaling_order_details (
    order_id, cluster_id, strategy_id, action_type, device_count,
    maintenance_start_time, maintenance_end_time, external_ticket_id,
    strategy_triggered_value, strategy_threshold_value,
    created_at, updated_at, deleted_at
)
SELECT 
    id, cluster_id, strategy_id, action_type, device_count,
    maintenance_start_time, maintenance_end_time, external_ticket_id,
    strategy_triggered_value, strategy_threshold_value,
    created_at, updated_at, deleted_at
FROM elastic_scaling_order;

-- 3. 更新关联表
-- 不需要更新order_device表，因为order_id保持不变
```

## 4. 服务层设计

### 4.1 通用订单服务接口

```go
// OrderService 通用订单服务接口
type OrderService interface {
    // 通用订单操作
    CreateOrder(ctx context.Context, order *Order) error
    GetOrderByID(ctx context.Context, id int64) (*Order, error)
    GetOrderByNumber(ctx context.Context, orderNumber string) (*Order, error)
    UpdateOrderStatus(ctx context.Context, id int64, status OrderStatus) error
    ListOrders(ctx context.Context, query OrderQuery) ([]Order, int64, error)
    
    // 订单处理流程
    ProcessOrder(ctx context.Context, id int64) error
    CompleteOrder(ctx context.Context, id int64) error
    FailOrder(ctx context.Context, id int64, reason string) error
    CancelOrder(ctx context.Context, id int64) error
    IgnoreOrder(ctx context.Context, id int64) error
}
```

### 4.2 弹性伸缩订单服务接口

```go
// ElasticScalingOrderService 弹性伸缩订单服务接口
type ElasticScalingOrderService interface {
    // 继承通用订单服务接口
    OrderService
    
    // 弹性伸缩特有操作
    CreateElasticScalingOrder(ctx context.Context, order *ElasticScalingOrderDetail) error
    GetElasticScalingOrderDetail(ctx context.Context, orderID int64) (*ElasticScalingOrderDetail, error)
    UpdateElasticScalingOrderDetail(ctx context.Context, detail *ElasticScalingOrderDetail) error
    
    // 业务流程
    ExecutePoolEntry(ctx context.Context, orderID int64) error
    ExecutePoolExit(ctx context.Context, orderID int64) error
}
```

## 5. API 接口设计

### 5.1 通用订单接口

```
GET    /api/orders                # 获取订单列表
POST   /api/orders                # 创建订单
GET    /api/orders/{id}           # 获取订单详情
PUT    /api/orders/{id}/status    # 更新订单状态
DELETE /api/orders/{id}           # 删除订单
```

### 5.2 弹性伸缩订单接口

```
POST   /api/elastic-scaling-orders              # 创建弹性伸缩订单
GET    /api/elastic-scaling-orders/{id}         # 获取弹性伸缩订单详情
PUT    /api/elastic-scaling-orders/{id}         # 更新弹性伸缩订单
POST   /api/elastic-scaling-orders/{id}/execute # 执行弹性伸缩订单
```

## 6. 前端适配

### 6.1 组件重构

1. 创建通用订单列表组件
2. 创建通用订单详情组件
3. 创建特定类型订单详情组件（如弹性伸缩订单详情）
4. 更新订单处理流程组件

### 6.2 路由设计

```
/orders                      # 订单列表页
/orders/:id                  # 订单详情页
/orders/create               # 创建订单页
/orders/elastic-scaling/:id  # 弹性伸缩订单详情页
```

## 7. 测试计划

1. 单元测试：测试新的模型和服务层代码
2. 集成测试：测试数据库迁移和 API 接口
3. 端到端测试：测试完整的订单处理流程
4. 性能测试：测试新的数据模型对查询性能的影响

## 8. 上线计划

1. 开发环境部署和测试
2. 测试环境部署和验证
3. 生产环境数据备份
4. 生产环境部署
5. 生产环境验证
6. 回滚计划（如需）

## 9. 未来扩展

基于此架构，可以轻松添加新的订单类型：

1. 定义新的订单类型枚举值
2. 创建新的订单详情表
3. 实现对应的服务层接口
4. 添加相应的 API 接口
5. 开发前端组件

例如，可以添加以下订单类型：

- 应用部署订单 (DeploymentOrderDetail)
- 配置变更订单 (ConfigChangeOrderDetail)
- 资源申请订单 (ResourceRequestOrderDetail)
- 故障处理订单 (IncidentOrderDetail)

## 10. 风险与缓解措施

| 风险 | 影响 | 缓解措施 |
|------|------|----------|
| 数据迁移失败 | 数据丢失或不一致 | 提前备份数据，编写回滚脚本，分批迁移 |
| 性能下降 | 查询速度变慢 | 添加适当的索引，优化查询，监控性能 |
| 代码兼容性问题 | 功能异常 | 全面的测试覆盖，灰度发布 |
| 前端适配不完全 | 用户体验下降 | 前端自动化测试，用户反馈收集 |

## 11. 时间线

| 阶段 | 时间估计 | 负责人 |
|------|----------|--------|
| 数据模型设计 | 1周 | 数据库工程师 |
| 后端代码重构 | 2周 | 后端开发团队 |
| 前端适配 | 2周 | 前端开发团队 |
| 测试 | 1周 | 测试团队 |
| 部署上线 | 1周 | 运维团队 |

总计：约7周