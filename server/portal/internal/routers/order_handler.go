package routers

import (
	"net/http"
	"strconv"
	"time"

	"navy-ng/models/portal"
	"navy-ng/pkg/middleware/render"
	"navy-ng/server/portal/internal/service"

	"github.com/gin-gonic/gin"
)

// OrderHandler 通用订单处理器
type OrderHandler struct {
	orderService service.OrderService
}

// NewOrderHandler 创建订单处理器实例
func NewOrderHandler(orderService service.OrderService) *OrderHandler {
	return &OrderHandler{
		orderService: orderService,
	}
}

// RegisterOrderRoutes 注册通用订单路由
func RegisterOrderRoutes(router *gin.RouterGroup, handler *OrderHandler) {
	orderGroup := router.Group("/orders")
	{
		orderGroup.GET("", handler.ListOrders)
		orderGroup.POST("", handler.CreateOrder)
		orderGroup.GET("/:id", handler.GetOrder)
		orderGroup.PUT("/:id/status", handler.UpdateOrderStatus)
		orderGroup.DELETE("/:id", handler.DeleteOrder)
	}
}

// ListOrders 获取订单列表
// @Summary 获取订单列表
// @Description 获取通用订单列表，支持分页和过滤
// @Tags 订单管理
// @Accept json
// @Produce json
// @Param type query string false "订单类型"
// @Param status query string false "订单状态"
// @Param createdBy query string false "创建人"
// @Param page query int false "页码" default(1)
// @Param pageSize query int false "每页数量" default(10)
// @Success 200 {object} render.Response
// @Router /fe-v1/orders [get]
func (h *OrderHandler) ListOrders(c *gin.Context) {
	// 解析查询参数
	query := service.OrderQuery{
		Type:      portal.OrderType(c.Query("type")),
		Status:    portal.OrderStatus(c.Query("status")),
		CreatedBy: c.Query("createdBy"),
		Page:      1,
		PageSize:  10,
	}

	if page := c.Query("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil && p > 0 {
			query.Page = p
		}
	}

	if pageSize := c.Query("pageSize"); pageSize != "" {
		if ps, err := strconv.Atoi(pageSize); err == nil && ps > 0 {
			query.PageSize = ps
		}
	}

	// 解析时间范围
	if startTime := c.Query("startTime"); startTime != "" {
		if t, err := time.Parse("2006-01-02", startTime); err == nil {
			query.StartTime = &t
		}
	}

	if endTime := c.Query("endTime"); endTime != "" {
		if t, err := time.Parse("2006-01-02", endTime); err == nil {
			query.EndTime = &t
		}
	}

	// 调用服务层
	orders, total, err := h.orderService.ListOrders(c.Request.Context(), query)
	if err != nil {
		render.Fail(c, http.StatusInternalServerError, err.Error())
		return
	}

	// 转换为DTO
	result := make([]service.BaseOrderDTO, len(orders))
	for i, order := range orders {
		result[i] = service.BaseOrderDTO{
			ID:            order.ID,
			OrderNumber:   order.OrderNumber,
			Name:          order.Name,
			Description:   order.Description,
			Type:          order.Type,
			Status:        order.Status,
			Executor:      order.Executor,
			CreatedBy:     order.CreatedBy,
			FailureReason: order.FailureReason,
			CreatedAt:     time.Time(order.CreatedAt),
			UpdatedAt:     time.Time(order.UpdatedAt),
		}

		if order.ExecutionTime != nil {
			execTime := time.Time(*order.ExecutionTime)
			result[i].ExecutionTime = &execTime
		}

		if order.CompletionTime != nil {
			complTime := time.Time(*order.CompletionTime)
			result[i].CompletionTime = &complTime
		}
	}

	render.Success(c, gin.H{
		"list":  result,
		"total": total,
	})
}

// CreateOrder 创建订单
// @Summary 创建订单
// @Description 创建新的通用订单
// @Tags 订单管理
// @Accept json
// @Produce json
// @Param order body portal.Order true "订单数据"
// @Success 200 {object} render.Response
// @Router /fe-v1/orders [post]
func (h *OrderHandler) CreateOrder(c *gin.Context) {
	var order portal.Order
	if err := c.ShouldBindJSON(&order); err != nil {
		render.BadRequest(c, err.Error())
		return
	}

	// 设置创建者
	if order.CreatedBy == "" {
		order.CreatedBy = "admin" // 实际环境中应该从认证信息获取
	}

	err := h.orderService.CreateOrder(c.Request.Context(), &order)
	if err != nil {
		render.Fail(c, http.StatusInternalServerError, err.Error())
		return
	}

	render.Success(c, gin.H{"id": order.ID})
}

// GetOrder 获取订单详情
// @Summary 获取订单详情
// @Description 根据ID获取订单详情
// @Tags 订单管理
// @Accept json
// @Produce json
// @Param id path int true "订单ID"
// @Success 200 {object} render.Response
// @Router /fe-v1/orders/{id} [get]
func (h *OrderHandler) GetOrder(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		render.BadRequest(c, "无效的订单ID")
		return
	}

	order, err := h.orderService.GetOrderByID(c.Request.Context(), id)
	if err != nil {
		render.Fail(c, http.StatusInternalServerError, err.Error())
		return
	}

	// 转换为DTO
	dto := service.BaseOrderDTO{
		ID:            order.ID,
		OrderNumber:   order.OrderNumber,
		Name:          order.Name,
		Description:   order.Description,
		Type:          order.Type,
		Status:        order.Status,
		Executor:      order.Executor,
		CreatedBy:     order.CreatedBy,
		FailureReason: order.FailureReason,
		CreatedAt:     time.Time(order.CreatedAt),
		UpdatedAt:     time.Time(order.UpdatedAt),
	}

	if order.ExecutionTime != nil {
		execTime := time.Time(*order.ExecutionTime)
		dto.ExecutionTime = &execTime
	}

	if order.CompletionTime != nil {
		complTime := time.Time(*order.CompletionTime)
		dto.CompletionTime = &complTime
	}

	render.Success(c, dto)
}

// UpdateOrderStatus 更新订单状态
// @Summary 更新订单状态
// @Description 更新订单状态
// @Tags 订单管理
// @Accept json
// @Produce json
// @Param id path int true "订单ID"
// @Param request body object true "状态更新请求"
// @Success 200 {object} render.Response
// @Router /fe-v1/orders/{id}/status [put]
func (h *OrderHandler) UpdateOrderStatus(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		render.BadRequest(c, "无效的订单ID")
		return
	}

	var reqBody struct {
		Status string `json:"status" binding:"required"`
		Reason string `json:"reason"`
	}

	if err := c.ShouldBindJSON(&reqBody); err != nil {
		render.BadRequest(c, err.Error())
		return
	}

	// 设置执行者
	executor := "admin" // 实际环境中应该从认证信息获取

	err = h.orderService.UpdateOrderStatus(c.Request.Context(), id, portal.OrderStatus(reqBody.Status), executor, reqBody.Reason)
	if err != nil {
		render.Fail(c, http.StatusInternalServerError, err.Error())
		return
	}

	render.Success(c, nil)
}

// DeleteOrder 删除订单
// @Summary 删除订单
// @Description 删除订单（软删除）
// @Tags 订单管理
// @Accept json
// @Produce json
// @Param id path int true "订单ID"
// @Success 200 {object} render.Response
// @Router /fe-v1/orders/{id} [delete]
func (h *OrderHandler) DeleteOrder(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		render.BadRequest(c, "无效的订单ID")
		return
	}

	err = h.orderService.DeleteOrder(c.Request.Context(), id)
	if err != nil {
		render.Fail(c, http.StatusInternalServerError, err.Error())
		return
	}

	render.Success(c, nil)
}
